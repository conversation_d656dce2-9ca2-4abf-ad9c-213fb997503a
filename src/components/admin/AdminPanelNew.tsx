import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/components/ui/use-toast";
import {
  BarChart3,
  Bot,
  Zap,
  Database,
  TrendingUp,
  Users,
  Building,
  MessageSquare,
  Server,
  Key,
  Settings,
} from "lucide-react";
import AuthService from "@/services/auth";
import RoutingRulesService from "@/services/routingRules";
import ApiKeyManagementService from "@/services/apiKeyManagement";
import UserManagementService from "@/services/userManagement";
import SystemPromptsService from "@/services/systemPrompts";
import {
  ApiKeyWithStatus,
  Agent,
  Tool,
  KnowledgeBase,
  Widget,
  HITLRequest,
  Analytics,
  Deployment,
  Session,
  Organization,
  Provider,
  SystemMetrics,
  UsageMetrics,
  PerformanceMetrics,
  User,
  RoutingRule,
  SystemPrompt,
} from "@/types";
import WebSocketStatusIndicator from "@/components/WebSocketStatusIndicator";

// Import tab components
import OverviewTab from "./tabs/OverviewTab";
import AgentsTab from "./tabs/AgentsTab";
import ToolsTab from "./tabs/ToolsTab";
import UsersTab from "./tabs/UsersTab";
import AnalyticsTab from "./tabs/AnalyticsTab";
import KnowledgeTab from "./tabs/KnowledgeTab";

interface AdminPanelProps {
  standalone?: boolean;
}

const AdminPanel: React.FC<AdminPanelProps> = ({ standalone = false }) => {
  const { toast } = useToast();

  // State for all data
  const [apiKeys, setApiKeys] = useState<ApiKeyWithStatus[]>([]);
  const [routingRules, setRoutingRules] = useState<RoutingRule[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [systemPrompts, setSystemPrompts] = useState<SystemPrompt[]>([]);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [tools, setTools] = useState<Tool[]>([]);
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([]);
  const [widgets, setWidgets] = useState<Widget[]>([]);
  const [hitlRequests, setHitlRequests] = useState<HITLRequest[]>([]);
  const [deployments, setDeployments] = useState<Deployment[]>([]);
  const [sessions, setSessions] = useState<Session[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [usageMetrics, setUsageMetrics] = useState<UsageMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");

  // Load initial data
  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Load mock data for demonstration
      await loadMockData();
      
      // In production, these would be real API calls:
      // const [apiKeysData, usersData, agentsData] = await Promise.all([
      //   ApiKeyManagementService.getApiKeys(),
      //   UserManagementService.getUsers(),
      //   // ... other service calls
      // ]);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load data");
      toast({
        title: "Error",
        description: "Failed to load admin data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadMockData = async () => {
    // Mock data for demonstration
    const mockUsers: User[] = [
      {
        id: "1",
        name: "John Doe",
        email: "<EMAIL>",
        role: "admin",
        permissions: ["admin:read", "admin:write"],
        createdAt: new Date("2024-01-15"),
        lastActive: new Date(),
      },
      {
        id: "2",
        name: "Jane Smith",
        email: "<EMAIL>",
        role: "agent_builder",
        permissions: ["agents:read", "agents:write"],
        createdAt: new Date("2024-02-01"),
        lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      },
    ];

    const mockAgents: Agent[] = [
      {
        id: "1",
        name: "Customer Support Agent",
        description: "Handles customer inquiries and support tickets",
        systemPrompt: "You are a helpful customer support agent...",
        model: "gpt-4",
        temperature: 0.7,
        maxTokens: 2000,
        tools: ["email", "ticket-system"],
        knowledgeBases: ["support-docs"],
        isPublic: false,
        isActive: true,
        createdBy: "1",
        metadata: {},
        createdAt: new Date("2024-01-20"),
        updatedAt: new Date(),
      },
    ];

    const mockTools: Tool[] = [
      {
        id: "1",
        name: "Email Integration",
        description: "Send and receive emails through SMTP",
        type: "api",
        schema: { parameters: {}, required: [], returns: {} },
        configuration: { endpoint: "https://api.email.com", method: "POST" },
        isActive: true,
        createdBy: "1",
        createdAt: new Date("2024-01-25"),
        updatedAt: new Date(),
      },
    ];

    const mockKnowledgeBases: KnowledgeBase[] = [
      {
        id: "1",
        name: "Support Documentation",
        description: "Customer support knowledge base",
        type: "documents",
        status: "active",
        documentsCount: 247,
        lastIndexed: new Date(),
        settings: {
          chunkSize: 1000,
          chunkOverlap: 200,
          embeddingModel: "text-embedding-ada-002",
          searchThreshold: 0.8,
          maxResults: 10,
          allowedFileTypes: ["pdf", "txt", "md"],
        },
        createdBy: "1",
        createdAt: new Date("2024-01-30"),
        updatedAt: new Date(),
      },
    ];

    const mockSessions: Session[] = [
      {
        id: "1",
        userId: "1",
        title: "Customer Support Chat",
        status: "active",
        messageCount: 15,
        tokensUsed: 2250,
        cost: 0.045,
        provider: "openai",
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        lastActivity: new Date(),
      },
    ];

    setUsers(mockUsers);
    setAgents(mockAgents);
    setTools(mockTools);
    setKnowledgeBases(mockKnowledgeBases);
    setSessions(mockSessions);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadAllData();
    setRefreshing(false);
    toast({
      title: "Success",
      description: "Data refreshed successfully",
    });
  };

  const handleToggleStatus = async (id: string, type: string) => {
    try {
      // In production, this would call the appropriate service
      toast({
        title: "Success",
        description: `${type} status updated successfully`,
      });
    } catch (err) {
      toast({
        title: "Error",
        description: `Failed to update ${type} status`,
        variant: "destructive",
      });
    }
  };

  const handleDeleteItem = async (id: string, type: string) => {
    try {
      // In production, this would call the appropriate service
      switch (type) {
        case "user":
          setUsers(users.filter(u => u.id !== id));
          break;
        case "agent":
          setAgents(agents.filter(a => a.id !== id));
          break;
        case "tool":
          setTools(tools.filter(t => t.id !== id));
          break;
        case "knowledgeBase":
          setKnowledgeBases(knowledgeBases.filter(kb => kb.id !== id));
          break;
      }
      
      toast({
        title: "Success",
        description: `${type} deleted successfully`,
      });
    } catch (err) {
      toast({
        title: "Error",
        description: `Failed to delete ${type}`,
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading admin panel...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-destructive mb-4">{error}</p>
          <button onClick={loadAllData} className="text-primary hover:underline">
            Try again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Admin Panel</h1>
          <p className="text-muted-foreground">
            Manage your AI system configuration and monitor performance
          </p>
        </div>
        <div className="flex items-center gap-2">
          <WebSocketStatusIndicator variant="badge" size="sm" />
        </div>
      </div>

      {/* Admin Content */}
      {standalone ? (
        // Standalone mode with tabs
        <Tabs defaultValue="overview" className="w-full">
          <div className="mb-6">
            <ScrollArea className="w-full whitespace-nowrap">
              <TabsList className="inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground">
                <TabsTrigger value="overview" className="flex items-center gap-2">
                  <BarChart3 size={16} />
                  Overview
                </TabsTrigger>
                <TabsTrigger value="agents" className="flex items-center gap-2">
                  <Bot size={16} />
                  Agents
                </TabsTrigger>
                <TabsTrigger value="tools" className="flex items-center gap-2">
                  <Zap size={16} />
                  Tools
                </TabsTrigger>
                <TabsTrigger value="knowledge" className="flex items-center gap-2">
                  <Database size={16} />
                  Knowledge
                </TabsTrigger>
                <TabsTrigger value="analytics" className="flex items-center gap-2">
                  <TrendingUp size={16} />
                  Analytics
                </TabsTrigger>
                <TabsTrigger value="users" className="flex items-center gap-2">
                  <Users size={16} />
                  Users
                </TabsTrigger>
              </TabsList>
            </ScrollArea>
          </div>

          {/* Tab Content */}
          <TabsContent value="overview">
            <OverviewTab
              users={users}
              systemMetrics={systemMetrics}
              sessions={sessions}
            />
          </TabsContent>

          <TabsContent value="agents">
            <AgentsTab
              agents={agents}
              onRefresh={handleRefresh}
              onToggleStatus={handleToggleStatus}
              onDeleteItem={handleDeleteItem}
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              filterStatus={filterStatus}
              onFilterChange={setFilterStatus}
            />
          </TabsContent>

          <TabsContent value="tools">
            <ToolsTab
              tools={tools}
              onRefresh={handleRefresh}
              onToggleStatus={handleToggleStatus}
              onDeleteItem={handleDeleteItem}
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              filterStatus={filterStatus}
              onFilterChange={setFilterStatus}
            />
          </TabsContent>

          <TabsContent value="knowledge">
            <KnowledgeTab
              knowledgeBases={knowledgeBases}
              onRefresh={handleRefresh}
              onDeleteItem={handleDeleteItem}
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              filterStatus={filterStatus}
              onFilterChange={setFilterStatus}
            />
          </TabsContent>

          <TabsContent value="analytics">
            <AnalyticsTab
              systemMetrics={systemMetrics}
              sessions={sessions}
              users={users}
            />
          </TabsContent>

          <TabsContent value="users">
            <UsersTab
              users={users}
              onRefresh={handleRefresh}
              onDeleteItem={handleDeleteItem}
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              filterStatus={filterStatus}
              onFilterChange={setFilterStatus}
            />
          </TabsContent>
        </Tabs>
      ) : (
        // Nested mode - just show the overview content without tabs
        <div className="w-full">
          <OverviewTab
            users={users}
            systemMetrics={systemMetrics}
            sessions={sessions}
          />
        </div>
      )}
    </div>
  );
};

export default AdminPanel;
