import { AIProvider, ProviderResponse, ToolCall } from "@/types";
import AuthService from "./auth";

interface ProviderConfig {
  apiKey: string;
  baseUrl?: string;
  model?: string;
  maxTokens?: number;
  temperature?: number;
  timeout?: number;
  retries?: number;
}

interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
}

interface UsageMetrics {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  cost: number;
  requestTime: number;
}

abstract class BaseProvider {
  protected config: ProviderConfig;
  protected provider: AIProvider;
  protected retryConfig: RetryConfig;
  private usageHistory: UsageMetrics[] = [];

  constructor(config: ProviderConfig, provider: AIProvider) {
    this.config = {
      timeout: 30000,
      retries: 3,
      ...config,
    };
    this.provider = provider;
    this.retryConfig = {
      maxRetries: config.retries || 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 2,
    };
  }

  abstract sendMessage(
    messages: Array<{ role: string; content: string }>,
    options?: {
      stream?: boolean;
      tools?: any[];
      onStream?: (chunk: string) => void;
      onToolCall?: (toolCall: ToolCall) => void;
    },
  ): Promise<ProviderResponse>;

  protected async makeRequest(
    url: string,
    options: RequestInit,
  ): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const response = await this.makeRequestWithRetry(url, {
        ...options,
        signal: controller.signal,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${this.config.apiKey}`,
          ...options.headers,
        },
      });

      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  private async makeRequestWithRetry(
    url: string,
    options: RequestInit,
  ): Promise<Response> {
    let lastError: Error;

    for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        const response = await fetch(url, options);

        if (response.ok) {
          return response;
        }

        // Handle specific HTTP errors
        if (response.status === 401) {
          throw new Error(
            `Authentication failed: Invalid API key for ${this.provider.name}`,
          );
        }
        if (response.status === 403) {
          throw new Error(
            `Access forbidden: Insufficient permissions for ${this.provider.name}`,
          );
        }
        if (response.status === 429) {
          const retryAfter = response.headers.get("retry-after");
          const delay = retryAfter
            ? parseInt(retryAfter) * 1000
            : this.calculateDelay(attempt);
          if (attempt < this.retryConfig.maxRetries) {
            await this.sleep(delay);
            continue;
          }
          throw new Error(`Rate limit exceeded for ${this.provider.name}`);
        }
        if (response.status >= 500) {
          if (attempt < this.retryConfig.maxRetries) {
            await this.sleep(this.calculateDelay(attempt));
            continue;
          }
          throw new Error(
            `Server error from ${this.provider.name}: ${response.status}`,
          );
        }

        throw new Error(
          `API request failed: ${response.status} ${response.statusText}`,
        );
      } catch (error) {
        lastError = error as Error;

        if (error instanceof TypeError && error.message.includes("fetch")) {
          // Network error - retry
          if (attempt < this.retryConfig.maxRetries) {
            await this.sleep(this.calculateDelay(attempt));
            continue;
          }
        }

        if (attempt === this.retryConfig.maxRetries) {
          throw lastError;
        }
      }
    }

    throw lastError!;
  }

  private calculateDelay(attempt: number): number {
    const delay = Math.min(
      this.retryConfig.baseDelay *
        Math.pow(this.retryConfig.backoffFactor, attempt),
      this.retryConfig.maxDelay,
    );
    // Add jitter to prevent thundering herd
    return delay + Math.random() * 1000;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  protected trackUsage(metrics: UsageMetrics): void {
    this.usageHistory.push({
      ...metrics,
      cost: this.calculateCost(metrics.promptTokens, metrics.completionTokens),
    });

    // Keep only last 1000 requests to prevent memory issues
    if (this.usageHistory.length > 1000) {
      this.usageHistory = this.usageHistory.slice(-1000);
    }
  }

  protected calculateCost(
    promptTokens: number,
    completionTokens: number,
  ): number {
    const inputCost = (promptTokens / 1000) * this.provider.costPerToken;
    const outputCost =
      (completionTokens / 1000) * this.provider.costPerToken * 2; // Output typically costs 2x
    return inputCost + outputCost;
  }

  public getUsageMetrics(): {
    totalRequests: number;
    totalTokens: number;
    totalCost: number;
    averageResponseTime: number;
    last24Hours: UsageMetrics[];
  } {
    const now = Date.now();
    const last24Hours = this.usageHistory.filter(
      (metric) => now - metric.requestTime < 24 * 60 * 60 * 1000,
    );

    return {
      totalRequests: this.usageHistory.length,
      totalTokens: this.usageHistory.reduce((sum, m) => sum + m.totalTokens, 0),
      totalCost: this.usageHistory.reduce((sum, m) => sum + m.cost, 0),
      averageResponseTime:
        this.usageHistory.length > 0
          ? this.usageHistory.reduce((sum, m) => sum + m.requestTime, 0) /
            this.usageHistory.length
          : 0,
      last24Hours,
    };
  }
}

class OpenAIProvider extends BaseProvider {
  async sendMessage(
    messages: Array<{ role: string; content: string }>,
    options: {
      stream?: boolean;
      tools?: any[];
      onStream?: (chunk: string) => void;
      onToolCall?: (toolCall: ToolCall) => void;
    } = {},
  ): Promise<ProviderResponse> {
    const startTime = Date.now();
    const url = `${this.config.baseUrl || "https://api.openai.com/v1"}/chat/completions`;

    const payload = {
      model: this.config.model || "gpt-4",
      messages: messages.map((msg) => ({
        role: msg.role,
        content: msg.content,
      })),
      max_tokens: this.config.maxTokens || 2000,
      temperature: this.config.temperature || 0.7,
      stream: options.stream || false,
      ...(options.tools &&
        options.tools.length > 0 && {
          tools: options.tools.map((tool) => ({
            type: "function",
            function: {
              name: tool.name,
              description: tool.description,
              parameters: tool.parameters || {},
            },
          })),
          tool_choice: "auto",
        }),
    };

    try {
      if (options.stream) {
        return await this.handleStreamingResponse(
          url,
          payload,
          options.onStream,
          options.onToolCall,
          startTime,
        );
      } else {
        return await this.handleRegularResponse(
          url,
          payload,
          options.onToolCall,
          startTime,
        );
      }
    } catch (error) {
      throw new Error(
        `OpenAI API Error: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  private async handleStreamingResponse(
    url: string,
    payload: any,
    onStream?: (chunk: string) => void,
    onToolCall?: (toolCall: ToolCall) => void,
    startTime: number = Date.now(),
  ): Promise<ProviderResponse> {
    const response = await this.makeRequest(url, {
      method: "POST",
      body: JSON.stringify(payload),
    });

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("No response body available for streaming");
    }

    let content = "";
    let totalTokens = 0;
    let promptTokens = 0;
    let completionTokens = 0;
    const decoder = new TextDecoder();
    const toolCalls: ToolCall[] = [];
    let currentToolCall: Partial<ToolCall> | null = null;

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n").filter((line) => line.trim());

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            const data = line.slice(6).trim();
            if (data === "[DONE]") continue;

            try {
              const parsed = JSON.parse(data);
              const choice = parsed.choices?.[0];

              if (choice?.delta) {
                const delta = choice.delta;

                // Handle content streaming
                if (delta.content) {
                  content += delta.content;
                  onStream?.(delta.content);
                }

                // Handle tool calls
                if (delta.tool_calls) {
                  for (const toolCallDelta of delta.tool_calls) {
                    if (toolCallDelta.index !== undefined) {
                      if (
                        !currentToolCall ||
                        toolCallDelta.index !== toolCalls.length - 1
                      ) {
                        currentToolCall = {
                          id:
                            toolCallDelta.id ||
                            `tool_${Date.now()}_${toolCallDelta.index}`,
                          tool: toolCallDelta.function?.name || "",
                          input: {},
                          status: "pending" as const,
                        };
                        toolCalls.push(currentToolCall as ToolCall);
                      }

                      if (toolCallDelta.function?.name) {
                        currentToolCall!.tool = toolCallDelta.function.name;
                      }

                      if (toolCallDelta.function?.arguments) {
                        try {
                          const args = JSON.parse(
                            toolCallDelta.function.arguments,
                          );
                          currentToolCall!.input = {
                            ...currentToolCall!.input,
                            ...args,
                          };
                        } catch {
                          // Partial JSON, continue accumulating
                        }
                      }
                    }
                  }
                }
              }

              // Track usage if available
              if (parsed.usage) {
                promptTokens = parsed.usage.prompt_tokens || 0;
                completionTokens = parsed.usage.completion_tokens || 0;
                totalTokens = parsed.usage.total_tokens || 0;
              }
            } catch (error) {
              console.warn(
                "Error parsing streaming chunk:",
                error,
                "Data:",
                data,
              );
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    // Execute tool calls if any
    for (const toolCall of toolCalls) {
      if (onToolCall) {
        try {
          onToolCall(toolCall);
          toolCall.status = "complete";
        } catch (error) {
          toolCall.status = "error";
          toolCall.output = {
            error:
              error instanceof Error ? error.message : "Tool execution failed",
          };
        }
      }
    }

    const requestTime = Date.now() - startTime;
    this.trackUsage({
      promptTokens,
      completionTokens,
      totalTokens: totalTokens || promptTokens + completionTokens,
      cost: 0, // Will be calculated in trackUsage
      requestTime,
    });

    return {
      content,
      isComplete: true,
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      usage: {
        promptTokens,
        completionTokens,
        totalTokens: totalTokens || promptTokens + completionTokens,
      },
    };
  }

  private async handleRegularResponse(
    url: string,
    payload: any,
    onToolCall?: (toolCall: ToolCall) => void,
    startTime: number = Date.now(),
  ): Promise<ProviderResponse> {
    const response = await this.makeRequest(url, {
      method: "POST",
      body: JSON.stringify(payload),
    });

    const data = await response.json();
    const choice = data.choices?.[0];

    if (!choice) {
      throw new Error("No response choices returned from OpenAI");
    }

    const content = choice.message?.content || "";
    const toolCalls: ToolCall[] = [];

    // Handle tool calls in non-streaming mode
    if (choice.message?.tool_calls) {
      for (const toolCall of choice.message.tool_calls) {
        const processedToolCall: ToolCall = {
          id: toolCall.id,
          tool: toolCall.function.name,
          input: JSON.parse(toolCall.function.arguments || "{}"),
          status: "pending",
        };

        toolCalls.push(processedToolCall);

        if (onToolCall) {
          try {
            onToolCall(processedToolCall);
            processedToolCall.status = "complete";
          } catch (error) {
            processedToolCall.status = "error";
            processedToolCall.output = {
              error:
                error instanceof Error
                  ? error.message
                  : "Tool execution failed",
            };
          }
        }
      }
    }

    const requestTime = Date.now() - startTime;
    const usage = {
      promptTokens: data.usage?.prompt_tokens || 0,
      completionTokens: data.usage?.completion_tokens || 0,
      totalTokens: data.usage?.total_tokens || 0,
    };

    this.trackUsage({
      ...usage,
      cost: 0, // Will be calculated in trackUsage
      requestTime,
    });

    return {
      content,
      isComplete: true,
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      usage,
    };
  }
}

class ClaudeProvider extends BaseProvider {
  async sendMessage(
    messages: Array<{ role: string; content: string }>,
    options: {
      stream?: boolean;
      tools?: any[];
      onStream?: (chunk: string) => void;
      onToolCall?: (toolCall: ToolCall) => void;
    } = {},
  ): Promise<ProviderResponse> {
    const startTime = Date.now();
    const url = `${this.config.baseUrl || "https://api.anthropic.com/v1"}/messages`;

    // Convert messages to Claude format
    const claudeMessages = messages
      .filter((msg) => msg.role !== "system")
      .map((msg) => ({
        role: msg.role === "assistant" ? "assistant" : "user",
        content: msg.content,
      }));

    // Extract system message if present
    const systemMessage = messages.find((msg) => msg.role === "system");

    const payload = {
      model: this.config.model || "claude-3-sonnet-20240229",
      messages: claudeMessages,
      max_tokens: this.config.maxTokens || 2000,
      temperature: this.config.temperature || 0.7,
      stream: options.stream || false,
      ...(systemMessage && { system: systemMessage.content }),
      ...(options.tools &&
        options.tools.length > 0 && {
          tools: options.tools.map((tool) => ({
            name: tool.name,
            description: tool.description,
            input_schema: tool.parameters || {},
          })),
        }),
    };

    try {
      const response = await this.makeRequest(url, {
        method: "POST",
        body: JSON.stringify(payload),
        headers: {
          "anthropic-version": "2023-06-01",
          "anthropic-beta": "tools-2024-04-04",
        },
      });

      if (options.stream) {
        return await this.handleStreamingResponse(
          response,
          options.onStream,
          options.onToolCall,
          startTime,
        );
      } else {
        return await this.handleRegularResponse(
          response,
          options.onToolCall,
          startTime,
        );
      }
    } catch (error) {
      throw new Error(
        `Claude API Error: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  private async handleStreamingResponse(
    response: Response,
    onStream?: (chunk: string) => void,
    onToolCall?: (toolCall: ToolCall) => void,
    startTime: number = Date.now(),
  ): Promise<ProviderResponse> {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("No response body available for streaming");
    }

    let content = "";
    let inputTokens = 0;
    let outputTokens = 0;
    const decoder = new TextDecoder();
    const toolCalls: ToolCall[] = [];

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n").filter((line) => line.trim());

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            const data = line.slice(6).trim();
            if (data === "[DONE]") continue;

            try {
              const parsed = JSON.parse(data);

              if (parsed.type === "content_block_delta" && parsed.delta?.text) {
                content += parsed.delta.text;
                onStream?.(parsed.delta.text);
              }

              if (
                parsed.type === "content_block_start" &&
                parsed.content_block?.type === "tool_use"
              ) {
                const toolCall: ToolCall = {
                  id: parsed.content_block.id,
                  tool: parsed.content_block.name,
                  input: parsed.content_block.input || {},
                  status: "pending",
                };
                toolCalls.push(toolCall);

                if (onToolCall) {
                  try {
                    onToolCall(toolCall);
                    toolCall.status = "complete";
                  } catch (error) {
                    toolCall.status = "error";
                    toolCall.output = {
                      error:
                        error instanceof Error
                          ? error.message
                          : "Tool execution failed",
                    };
                  }
                }
              }

              if (parsed.type === "message_delta" && parsed.usage) {
                outputTokens = parsed.usage.output_tokens || 0;
              }

              if (parsed.type === "message_start" && parsed.message?.usage) {
                inputTokens = parsed.message.usage.input_tokens || 0;
              }
            } catch (error) {
              console.warn(
                "Error parsing Claude streaming chunk:",
                error,
                "Data:",
                data,
              );
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    const requestTime = Date.now() - startTime;
    this.trackUsage({
      promptTokens: inputTokens,
      completionTokens: outputTokens,
      totalTokens: inputTokens + outputTokens,
      cost: 0, // Will be calculated in trackUsage
      requestTime,
    });

    return {
      content,
      isComplete: true,
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      usage: {
        promptTokens: inputTokens,
        completionTokens: outputTokens,
        totalTokens: inputTokens + outputTokens,
      },
    };
  }

  private async handleRegularResponse(
    response: Response,
    onToolCall?: (toolCall: ToolCall) => void,
    startTime: number = Date.now(),
  ): Promise<ProviderResponse> {
    const data = await response.json();

    let content = "";
    const toolCalls: ToolCall[] = [];

    if (data.content) {
      for (const block of data.content) {
        if (block.type === "text") {
          content += block.text;
        } else if (block.type === "tool_use") {
          const toolCall: ToolCall = {
            id: block.id,
            tool: block.name,
            input: block.input || {},
            status: "pending",
          };
          toolCalls.push(toolCall);

          if (onToolCall) {
            try {
              onToolCall(toolCall);
              toolCall.status = "complete";
            } catch (error) {
              toolCall.status = "error";
              toolCall.output = {
                error:
                  error instanceof Error
                    ? error.message
                    : "Tool execution failed",
              };
            }
          }
        }
      }
    }

    const requestTime = Date.now() - startTime;
    const usage = {
      promptTokens: data.usage?.input_tokens || 0,
      completionTokens: data.usage?.output_tokens || 0,
      totalTokens:
        (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0),
    };

    this.trackUsage({
      ...usage,
      cost: 0, // Will be calculated in trackUsage
      requestTime,
    });

    return {
      content,
      isComplete: true,
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      usage,
    };
  }
}

class GeminiProvider extends BaseProvider {
  async sendMessage(
    messages: Array<{ role: string; content: string }>,
    options: {
      stream?: boolean;
      tools?: any[];
      onStream?: (chunk: string) => void;
      onToolCall?: (toolCall: ToolCall) => void;
    } = {},
  ): Promise<ProviderResponse> {
    const startTime = Date.now();
    const baseUrl =
      this.config.baseUrl || "https://generativelanguage.googleapis.com/v1beta";
    const model = this.config.model || "gemini-pro";

    // Convert messages to Gemini format
    const contents = [];
    let systemInstruction = "";

    for (const msg of messages) {
      if (msg.role === "system") {
        systemInstruction = msg.content;
      } else {
        contents.push({
          parts: [{ text: msg.content }],
          role: msg.role === "assistant" ? "model" : "user",
        });
      }
    }

    const payload = {
      contents,
      generationConfig: {
        maxOutputTokens: this.config.maxTokens || 2000,
        temperature: this.config.temperature || 0.7,
      },
      ...(systemInstruction && {
        systemInstruction: { parts: [{ text: systemInstruction }] },
      }),
      ...(options.tools &&
        options.tools.length > 0 && {
          tools: [
            {
              functionDeclarations: options.tools.map((tool) => ({
                name: tool.name,
                description: tool.description,
                parameters: tool.parameters || {},
              })),
            },
          ],
        }),
    };

    try {
      if (options.stream) {
        return await this.handleStreamingResponse(
          baseUrl,
          model,
          payload,
          options.onStream,
          options.onToolCall,
          startTime,
        );
      } else {
        return await this.handleRegularResponse(
          baseUrl,
          model,
          payload,
          options.onToolCall,
          startTime,
        );
      }
    } catch (error) {
      throw new Error(
        `Gemini API Error: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  private async handleStreamingResponse(
    baseUrl: string,
    model: string,
    payload: any,
    onStream?: (chunk: string) => void,
    onToolCall?: (toolCall: ToolCall) => void,
    startTime: number = Date.now(),
  ): Promise<ProviderResponse> {
    const url = `${baseUrl}/models/${model}:streamGenerateContent?key=${this.config.apiKey}`;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          `Gemini API request failed: ${response.status} - ${errorData.error?.message || response.statusText}`,
        );
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("No response body available for streaming");
      }

      let content = "";
      let promptTokens = 0;
      let candidatesTokens = 0;
      const decoder = new TextDecoder();
      const toolCalls: ToolCall[] = [];

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split("\n").filter((line) => line.trim());

          for (const line of lines) {
            try {
              const parsed = JSON.parse(line);
              const candidate = parsed.candidates?.[0];

              if (candidate?.content?.parts) {
                for (const part of candidate.content.parts) {
                  if (part.text) {
                    content += part.text;
                    onStream?.(part.text);
                  }

                  if (part.functionCall && onToolCall) {
                    const toolCall: ToolCall = {
                      id: `tool_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                      tool: part.functionCall.name,
                      input: part.functionCall.args || {},
                      status: "pending",
                    };
                    toolCalls.push(toolCall);

                    try {
                      onToolCall(toolCall);
                      toolCall.status = "complete";
                    } catch (error) {
                      toolCall.status = "error";
                      toolCall.output = {
                        error:
                          error instanceof Error
                            ? error.message
                            : "Tool execution failed",
                      };
                    }
                  }
                }
              }

              if (parsed.usageMetadata) {
                promptTokens = parsed.usageMetadata.promptTokenCount || 0;
                candidatesTokens =
                  parsed.usageMetadata.candidatesTokenCount || 0;
              }
            } catch (error) {
              // Skip invalid JSON lines
              console.warn("Error parsing Gemini streaming chunk:", error);
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      const requestTime = Date.now() - startTime;
      this.trackUsage({
        promptTokens,
        completionTokens: candidatesTokens,
        totalTokens: promptTokens + candidatesTokens,
        cost: 0, // Will be calculated in trackUsage
        requestTime,
      });

      return {
        content,
        isComplete: true,
        toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
        usage: {
          promptTokens,
          completionTokens: candidatesTokens,
          totalTokens: promptTokens + candidatesTokens,
        },
      };
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  private async handleRegularResponse(
    baseUrl: string,
    model: string,
    payload: any,
    onToolCall?: (toolCall: ToolCall) => void,
    startTime: number = Date.now(),
  ): Promise<ProviderResponse> {
    const url = `${baseUrl}/models/${model}:generateContent?key=${this.config.apiKey}`;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          `Gemini API request failed: ${response.status} - ${errorData.error?.message || response.statusText}`,
        );
      }

      const data = await response.json();
      const candidate = data.candidates?.[0];

      if (!candidate) {
        throw new Error("No response candidates returned from Gemini");
      }

      let content = "";
      const toolCalls: ToolCall[] = [];

      if (candidate.content?.parts) {
        for (const part of candidate.content.parts) {
          if (part.text) {
            content += part.text;
          }

          if (part.functionCall && onToolCall) {
            const toolCall: ToolCall = {
              id: `tool_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              tool: part.functionCall.name,
              input: part.functionCall.args || {},
              status: "pending",
            };
            toolCalls.push(toolCall);

            try {
              onToolCall(toolCall);
              toolCall.status = "complete";
            } catch (error) {
              toolCall.status = "error";
              toolCall.output = {
                error:
                  error instanceof Error
                    ? error.message
                    : "Tool execution failed",
              };
            }
          }
        }
      }

      const requestTime = Date.now() - startTime;
      const usage = {
        promptTokens: data.usageMetadata?.promptTokenCount || 0,
        completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
        totalTokens: data.usageMetadata?.totalTokenCount || 0,
      };

      this.trackUsage({
        ...usage,
        cost: 0, // Will be calculated in trackUsage
        requestTime,
      });

      return {
        content,
        isComplete: true,
        toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
        usage,
      };
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }
}

class MistralProvider extends BaseProvider {
  async sendMessage(
    messages: Array<{ role: string; content: string }>,
    options: {
      stream?: boolean;
      tools?: any[];
      onStream?: (chunk: string) => void;
      onToolCall?: (toolCall: ToolCall) => void;
    } = {},
  ): Promise<ProviderResponse> {
    const startTime = Date.now();
    const url = `${this.config.baseUrl || "https://api.mistral.ai/v1"}/chat/completions`;

    const payload = {
      model: this.config.model || "mistral-medium-latest",
      messages: messages.map((msg) => ({
        role: msg.role,
        content: msg.content,
      })),
      max_tokens: this.config.maxTokens || 2000,
      temperature: this.config.temperature || 0.7,
      stream: options.stream || false,
      ...(options.tools &&
        options.tools.length > 0 && {
          tools: options.tools.map((tool) => ({
            type: "function",
            function: {
              name: tool.name,
              description: tool.description,
              parameters: tool.parameters || {},
            },
          })),
          tool_choice: "auto",
        }),
    };

    try {
      if (options.stream) {
        return await this.handleStreamingResponse(
          url,
          payload,
          options.onStream,
          options.onToolCall,
          startTime,
        );
      } else {
        return await this.handleRegularResponse(
          url,
          payload,
          options.onToolCall,
          startTime,
        );
      }
    } catch (error) {
      throw new Error(
        `Mistral API Error: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  private async handleStreamingResponse(
    url: string,
    payload: any,
    onStream?: (chunk: string) => void,
    onToolCall?: (toolCall: ToolCall) => void,
    startTime: number = Date.now(),
  ): Promise<ProviderResponse> {
    const response = await this.makeRequest(url, {
      method: "POST",
      body: JSON.stringify(payload),
    });

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("No response body available for streaming");
    }

    let content = "";
    let totalTokens = 0;
    let promptTokens = 0;
    let completionTokens = 0;
    const decoder = new TextDecoder();
    const toolCalls: ToolCall[] = [];
    let currentToolCall: Partial<ToolCall> | null = null;

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n").filter((line) => line.trim());

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            const data = line.slice(6).trim();
            if (data === "[DONE]") continue;

            try {
              const parsed = JSON.parse(data);
              const choice = parsed.choices?.[0];

              if (choice?.delta) {
                const delta = choice.delta;

                // Handle content streaming
                if (delta.content) {
                  content += delta.content;
                  onStream?.(delta.content);
                }

                // Handle tool calls
                if (delta.tool_calls) {
                  for (const toolCallDelta of delta.tool_calls) {
                    if (toolCallDelta.index !== undefined) {
                      if (
                        !currentToolCall ||
                        toolCallDelta.index !== toolCalls.length - 1
                      ) {
                        currentToolCall = {
                          id:
                            toolCallDelta.id ||
                            `tool_${Date.now()}_${toolCallDelta.index}`,
                          tool: toolCallDelta.function?.name || "",
                          input: {},
                          status: "pending" as const,
                        };
                        toolCalls.push(currentToolCall as ToolCall);
                      }

                      if (toolCallDelta.function?.name) {
                        currentToolCall!.tool = toolCallDelta.function.name;
                      }

                      if (toolCallDelta.function?.arguments) {
                        try {
                          const args = JSON.parse(
                            toolCallDelta.function.arguments,
                          );
                          currentToolCall!.input = {
                            ...currentToolCall!.input,
                            ...args,
                          };
                        } catch {
                          // Partial JSON, continue accumulating
                        }
                      }
                    }
                  }
                }
              }

              // Track usage if available
              if (parsed.usage) {
                promptTokens = parsed.usage.prompt_tokens || 0;
                completionTokens = parsed.usage.completion_tokens || 0;
                totalTokens = parsed.usage.total_tokens || 0;
              }
            } catch (error) {
              console.warn(
                "Error parsing Mistral streaming chunk:",
                error,
                "Data:",
                data,
              );
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    // Execute tool calls if any
    for (const toolCall of toolCalls) {
      if (onToolCall) {
        try {
          onToolCall(toolCall);
          toolCall.status = "complete";
        } catch (error) {
          toolCall.status = "error";
          toolCall.output = {
            error:
              error instanceof Error ? error.message : "Tool execution failed",
          };
        }
      }
    }

    const requestTime = Date.now() - startTime;
    this.trackUsage({
      promptTokens,
      completionTokens,
      totalTokens: totalTokens || promptTokens + completionTokens,
      cost: 0, // Will be calculated in trackUsage
      requestTime,
    });

    return {
      content,
      isComplete: true,
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      usage: {
        promptTokens,
        completionTokens,
        totalTokens: totalTokens || promptTokens + completionTokens,
      },
    };
  }

  private async handleRegularResponse(
    url: string,
    payload: any,
    onToolCall?: (toolCall: ToolCall) => void,
    startTime: number = Date.now(),
  ): Promise<ProviderResponse> {
    const response = await this.makeRequest(url, {
      method: "POST",
      body: JSON.stringify(payload),
    });

    const data = await response.json();
    const choice = data.choices?.[0];

    if (!choice) {
      throw new Error("No response choices returned from Mistral");
    }

    const content = choice.message?.content || "";
    const toolCalls: ToolCall[] = [];

    // Handle tool calls in non-streaming mode
    if (choice.message?.tool_calls) {
      for (const toolCall of choice.message.tool_calls) {
        const processedToolCall: ToolCall = {
          id: toolCall.id,
          tool: toolCall.function.name,
          input: JSON.parse(toolCall.function.arguments || "{}"),
          status: "pending",
        };

        toolCalls.push(processedToolCall);

        if (onToolCall) {
          try {
            onToolCall(processedToolCall);
            processedToolCall.status = "complete";
          } catch (error) {
            processedToolCall.status = "error";
            processedToolCall.output = {
              error:
                error instanceof Error
                  ? error.message
                  : "Tool execution failed",
            };
          }
        }
      }
    }

    const requestTime = Date.now() - startTime;
    const usage = {
      promptTokens: data.usage?.prompt_tokens || 0,
      completionTokens: data.usage?.completion_tokens || 0,
      totalTokens: data.usage?.total_tokens || 0,
    };

    this.trackUsage({
      ...usage,
      cost: 0, // Will be calculated in trackUsage
      requestTime,
    });

    return {
      content,
      isComplete: true,
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      usage,
    };
  }
}

class GroqProvider extends BaseProvider {
  async sendMessage(
    messages: Array<{ role: string; content: string }>,
    options: {
      stream?: boolean;
      tools?: any[];
      onStream?: (chunk: string) => void;
      onToolCall?: (toolCall: ToolCall) => void;
    } = {},
  ): Promise<ProviderResponse> {
    const startTime = Date.now();
    const url = `${this.config.baseUrl || "https://api.groq.com/openai/v1"}/chat/completions`;

    const payload = {
      model: this.config.model || "mixtral-8x7b-32768",
      messages: messages.map((msg) => ({
        role: msg.role,
        content: msg.content,
      })),
      max_tokens: this.config.maxTokens || 2000,
      temperature: this.config.temperature || 0.7,
      stream: options.stream || false,
      ...(options.tools &&
        options.tools.length > 0 && {
          tools: options.tools.map((tool) => ({
            type: "function",
            function: {
              name: tool.name,
              description: tool.description,
              parameters: tool.parameters || {},
            },
          })),
          tool_choice: "auto",
        }),
    };

    try {
      if (options.stream) {
        return await this.handleStreamingResponse(
          url,
          payload,
          options.onStream,
          options.onToolCall,
          startTime,
        );
      } else {
        return await this.handleRegularResponse(
          url,
          payload,
          options.onToolCall,
          startTime,
        );
      }
    } catch (error) {
      throw new Error(
        `Groq API Error: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  private async handleStreamingResponse(
    url: string,
    payload: any,
    onStream?: (chunk: string) => void,
    onToolCall?: (toolCall: ToolCall) => void,
    startTime: number = Date.now(),
  ): Promise<ProviderResponse> {
    const response = await this.makeRequest(url, {
      method: "POST",
      body: JSON.stringify(payload),
    });

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("No response body available for streaming");
    }

    let content = "";
    let totalTokens = 0;
    let promptTokens = 0;
    let completionTokens = 0;
    const decoder = new TextDecoder();
    const toolCalls: ToolCall[] = [];
    let currentToolCall: Partial<ToolCall> | null = null;

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n").filter((line) => line.trim());

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            const data = line.slice(6).trim();
            if (data === "[DONE]") continue;

            try {
              const parsed = JSON.parse(data);
              const choice = parsed.choices?.[0];

              if (choice?.delta) {
                const delta = choice.delta;

                // Handle content streaming
                if (delta.content) {
                  content += delta.content;
                  onStream?.(delta.content);
                }

                // Handle tool calls
                if (delta.tool_calls) {
                  for (const toolCallDelta of delta.tool_calls) {
                    if (toolCallDelta.index !== undefined) {
                      if (
                        !currentToolCall ||
                        toolCallDelta.index !== toolCalls.length - 1
                      ) {
                        currentToolCall = {
                          id:
                            toolCallDelta.id ||
                            `tool_${Date.now()}_${toolCallDelta.index}`,
                          tool: toolCallDelta.function?.name || "",
                          input: {},
                          status: "pending" as const,
                        };
                        toolCalls.push(currentToolCall as ToolCall);
                      }

                      if (toolCallDelta.function?.name) {
                        currentToolCall!.tool = toolCallDelta.function.name;
                      }

                      if (toolCallDelta.function?.arguments) {
                        try {
                          const args = JSON.parse(
                            toolCallDelta.function.arguments,
                          );
                          currentToolCall!.input = {
                            ...currentToolCall!.input,
                            ...args,
                          };
                        } catch {
                          // Partial JSON, continue accumulating
                        }
                      }
                    }
                  }
                }
              }

              // Track usage if available
              if (parsed.usage) {
                promptTokens = parsed.usage.prompt_tokens || 0;
                completionTokens = parsed.usage.completion_tokens || 0;
                totalTokens = parsed.usage.total_tokens || 0;
              }
            } catch (error) {
              console.warn(
                "Error parsing Groq streaming chunk:",
                error,
                "Data:",
                data,
              );
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    // Execute tool calls if any
    for (const toolCall of toolCalls) {
      if (onToolCall) {
        try {
          onToolCall(toolCall);
          toolCall.status = "complete";
        } catch (error) {
          toolCall.status = "error";
          toolCall.output = {
            error:
              error instanceof Error ? error.message : "Tool execution failed",
          };
        }
      }
    }

    const requestTime = Date.now() - startTime;
    this.trackUsage({
      promptTokens,
      completionTokens,
      totalTokens: totalTokens || promptTokens + completionTokens,
      cost: 0, // Will be calculated in trackUsage
      requestTime,
    });

    return {
      content,
      isComplete: true,
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      usage: {
        promptTokens,
        completionTokens,
        totalTokens: totalTokens || promptTokens + completionTokens,
      },
    };
  }

  private async handleRegularResponse(
    url: string,
    payload: any,
    onToolCall?: (toolCall: ToolCall) => void,
    startTime: number = Date.now(),
  ): Promise<ProviderResponse> {
    const response = await this.makeRequest(url, {
      method: "POST",
      body: JSON.stringify(payload),
    });

    const data = await response.json();
    const choice = data.choices?.[0];

    if (!choice) {
      throw new Error("No response choices returned from Groq");
    }

    const content = choice.message?.content || "";
    const toolCalls: ToolCall[] = [];

    // Handle tool calls in non-streaming mode
    if (choice.message?.tool_calls) {
      for (const toolCall of choice.message.tool_calls) {
        const processedToolCall: ToolCall = {
          id: toolCall.id,
          tool: toolCall.function.name,
          input: JSON.parse(toolCall.function.arguments || "{}"),
          status: "pending",
        };

        toolCalls.push(processedToolCall);

        if (onToolCall) {
          try {
            onToolCall(processedToolCall);
            processedToolCall.status = "complete";
          } catch (error) {
            processedToolCall.status = "error";
            processedToolCall.output = {
              error:
                error instanceof Error
                  ? error.message
                  : "Tool execution failed",
            };
          }
        }
      }
    }

    const requestTime = Date.now() - startTime;
    const usage = {
      promptTokens: data.usage?.prompt_tokens || 0,
      completionTokens: data.usage?.completion_tokens || 0,
      totalTokens: data.usage?.total_tokens || 0,
    };

    this.trackUsage({
      ...usage,
      cost: 0, // Will be calculated in trackUsage
      requestTime,
    });

    return {
      content,
      isComplete: true,
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      usage,
    };
  }
}

class APIProviderService {
  private static instance: APIProviderService;
  private providers: Map<string, BaseProvider> = new Map();
  private providerConfigs: Map<string, AIProvider> = new Map();

  static getInstance(): APIProviderService {
    if (!APIProviderService.instance) {
      APIProviderService.instance = new APIProviderService();
    }
    return APIProviderService.instance;
  }

  constructor() {
    this.initializeProviders();
  }

  private initializeProviders() {
    this.providerConfigs.set("openai", {
      id: "openai",
      name: "OpenAI",
      baseUrl: "https://api.openai.com/v1",
      supportsStreaming: true,
      supportsToolCalls: true,
      maxTokens: 4000,
      costPerToken: 0.00002,
    });

    this.providerConfigs.set("claude", {
      id: "claude",
      name: "Claude",
      baseUrl: "https://api.anthropic.com/v1",
      supportsStreaming: true,
      supportsToolCalls: false,
      maxTokens: 4000,
      costPerToken: 0.000015,
    });

    this.providerConfigs.set("gemini", {
      id: "gemini",
      name: "Gemini",
      baseUrl: "https://generativelanguage.googleapis.com/v1beta",
      supportsStreaming: false,
      supportsToolCalls: false,
      maxTokens: 2048,
      costPerToken: 0.00001,
    });

    this.providerConfigs.set("mistral", {
      id: "mistral",
      name: "Mistral",
      baseUrl: "https://api.mistral.ai/v1",
      supportsStreaming: true,
      supportsToolCalls: true,
      maxTokens: 4000,
      costPerToken: 0.000007,
    });

    this.providerConfigs.set("groq", {
      id: "groq",
      name: "Groq",
      baseUrl: "https://api.groq.com/openai/v1",
      supportsStreaming: true,
      supportsToolCalls: true,
      maxTokens: 32768,
      costPerToken: 0.0000002,
    });
  }

  async initializeProvider(providerId: string, apiKey: string): Promise<void> {
    const config = this.providerConfigs.get(providerId);
    if (!config) {
      throw new Error(`Unknown provider: ${providerId}`);
    }

    let provider: BaseProvider;

    switch (providerId) {
      case "openai":
        provider = new OpenAIProvider({ apiKey }, config);
        break;
      case "claude":
        provider = new ClaudeProvider({ apiKey }, config);
        break;
      case "gemini":
        provider = new GeminiProvider({ apiKey }, config);
        break;
      case "mistral":
        provider = new MistralProvider({ apiKey }, config);
        break;
      case "groq":
        provider = new GroqProvider({ apiKey }, config);
        break;
      default:
        throw new Error(`Provider ${providerId} not implemented`);
    }

    this.providers.set(providerId, provider);
  }

  getProvider(providerId: string): BaseProvider | null {
    return this.providers.get(providerId) || null;
  }

  getAvailableProviders(): AIProvider[] {
    return Array.from(this.providerConfigs.values());
  }

  async sendMessage(
    providerId: string,
    messages: Array<{ role: string; content: string }>,
    options?: any,
  ): Promise<ProviderResponse> {
    const provider = this.getProvider(providerId);
    if (!provider) {
      throw new Error(`Provider ${providerId} not initialized`);
    }

    return provider.sendMessage(messages, options);
  }
}

export default APIProviderService.getInstance();
